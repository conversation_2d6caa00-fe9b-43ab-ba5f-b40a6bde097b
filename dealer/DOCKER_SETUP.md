# DealerLink Docker Setup Guide

## 🚀 Quick Start

### Prerequisites
1. **Install Docker Desktop**
   ```bash
   # Run our installation helper (macOS)
   ./scripts/install-docker-mac.sh
   
   # Or install manually from: https://www.docker.com/products/docker-desktop/
   ```

2. **Start Docker Desktop** from Applications folder

3. **Verify Docker installation**
   ```bash
   docker --version
   docker compose version
   ```

### Launch DealerLink

```bash
# Start development environment
./scripts/docker-dev.sh start-bg

# Or manually:
docker compose -f docker-compose.dev.yml up --build -d
```

**Access your application:**
- 🌐 **Frontend**: http://localhost:5173
- 🔧 **Backend API**: http://localhost:8000
- 👤 **Admin Panel**: http://localhost:8000/admin

## 📋 What Was Fixed

### 1. **Database Configuration Issues**
- ✅ Fixed PostgreSQL vs SQLite configuration mismatch
- ✅ Added environment-based database switching
- ✅ Configured proper database settings for Docker

### 2. **Missing Dependencies**
- ✅ Created `requirements.txt` with all Python dependencies
- ✅ Added `dj-database-url` for production database URLs
- ✅ Added `gunicorn` for production WSGI server

### 3. **Environment Configuration**
- ✅ Added environment variable support
- ✅ Created `.env.example` template
- ✅ Configured CORS for frontend-backend communication
- ✅ Added static files configuration

### 4. **Node.js/npm Issues**
- ✅ Solved by containerizing with Docker
- ✅ No need to install Node.js locally
- ✅ Consistent development environment

## 🐳 Docker Implementation

### Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │  Django Backend │    │  React Frontend │
│   (Database)    │◄───┤   (API Server)  │◄───┤   (Web Client)  │
│   Port: 5432    │    │   Port: 8000    │    │   Port: 5173    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Files Created

#### Docker Configuration
- `docker-compose.yml` - Production setup
- `docker-compose.dev.yml` - Development setup
- `carconnect_backend/Dockerfile` - Backend production image
- `carconnect_backend/Dockerfile.dev` - Backend development image
- `dealerlink-frontend/Dockerfile` - Frontend production image
- `dealerlink-frontend/Dockerfile.dev` - Frontend development image

#### Helper Scripts
- `scripts/docker-dev.sh` - Development helper script
- `scripts/install-docker-mac.sh` - Docker installation helper

#### Configuration Files
- `.env.example` - Environment variables template
- `dealerlink-frontend/nginx.conf` - Nginx configuration for production
- `.dockerignore` files - Optimize Docker builds

## 🛠 Development Workflow

### Common Commands

```bash
# Start development environment
./scripts/docker-dev.sh start-bg

# View logs
./scripts/docker-dev.sh logs

# Run Django migrations
./scripts/docker-dev.sh migrate

# Create superuser
./scripts/docker-dev.sh createsuperuser

# Stop services
./scripts/docker-dev.sh stop

# Restart services
./scripts/docker-dev.sh restart

# Run any Django command
./scripts/docker-dev.sh django collectstatic
./scripts/docker-dev.sh django shell
```

### Manual Docker Commands

```bash
# Start development
docker compose -f docker-compose.dev.yml up --build -d

# View logs
docker compose -f docker-compose.dev.yml logs -f

# Execute Django commands
docker compose -f docker-compose.dev.yml exec backend python manage.py migrate
docker compose -f docker-compose.dev.yml exec backend python manage.py createsuperuser

# Stop services
docker compose -f docker-compose.dev.yml down
```

## 🔧 Configuration Details

### Environment Variables

The application now supports environment-based configuration:

```bash
# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1

# Database (PostgreSQL)
USE_POSTGRES=True
DB_NAME=dealerlink_db
DB_USER=dealerlink_user
DB_PASSWORD=dealerlink_pass
DB_HOST=db
DB_PORT=5432

# Frontend
VITE_API_URL=http://localhost:8000
```

### Database Switching

The application automatically chooses the database:

1. **Production**: Uses `DATABASE_URL` if set
2. **Development with PostgreSQL**: Uses `USE_POSTGRES=True`
3. **Development with SQLite**: Default fallback

### CORS Configuration

Configured to allow frontend-backend communication:
- Development: Allows all origins
- Production: Specific allowed origins

## 🚀 Production Deployment

### Using Production Docker Compose

```bash
# Build and start production services
docker compose up --build -d

# Access application
# Frontend: http://localhost:3000
# Backend: http://localhost:8000
```

### Environment Setup for Production

1. Copy environment template:
   ```bash
   cp .env.example .env
   ```

2. Update production values:
   ```bash
   DEBUG=False
   SECRET_KEY=your-production-secret-key
   ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
   DATABASE_URL=postgres://user:password@host:port/database
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Docker not found**
   ```bash
   # Install Docker Desktop
   ./scripts/install-docker-mac.sh
   ```

2. **Port already in use**
   ```bash
   # Stop existing services
   docker compose -f docker-compose.dev.yml down
   
   # Or change ports in docker-compose.dev.yml
   ```

3. **Database connection errors**
   ```bash
   # Restart database service
   docker compose -f docker-compose.dev.yml restart db
   
   # Check database logs
   docker compose -f docker-compose.dev.yml logs db
   ```

4. **Frontend build errors**
   ```bash
   # Rebuild frontend
   docker compose -f docker-compose.dev.yml build frontend
   
   # Check frontend logs
   docker compose -f docker-compose.dev.yml logs frontend
   ```

### Useful Commands

```bash
# View all running containers
docker ps

# Clean up unused Docker resources
docker system prune

# View Docker images
docker images

# Remove specific container
docker rm container_name

# View container logs
docker logs container_name
```

## 📚 Next Steps

1. **Create superuser**: `./scripts/docker-dev.sh createsuperuser`
2. **Add sample data**: Create fixtures or use Django admin
3. **Customize settings**: Update `.env` file as needed
4. **Deploy to production**: Use production docker-compose.yml

## 🎯 Benefits of This Setup

- ✅ **Consistent Environment**: Same setup across all machines
- ✅ **Easy Setup**: One command to start everything
- ✅ **No Local Dependencies**: No need to install Node.js, PostgreSQL locally
- ✅ **Production Ready**: Same containers work in production
- ✅ **Isolated**: No conflicts with other projects
- ✅ **Scalable**: Easy to add more services (Redis, Celery, etc.)

Your DealerLink application is now fully containerized and ready for development! 🎉
