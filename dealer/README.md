# DealerLink - Peer-to-Peer Trading Platform

A modern peer-to-peer trading platform built with Django REST Framework and React, similar to Facebook Marketplace where individuals can trade and deal with each other directly.

## Features

- **User Authentication & Profiles**: Custom user model with location-based features
- **Car Listings**: Create, edit, and manage car listings with images
- **Search & Filter**: Advanced search and filtering capabilities
- **Messaging System**: Direct communication between buyers and sellers
- **Saved Cars**: Bookmark interesting listings
- **Analytics Dashboard**: Track views, inquiries, and performance
- **Responsive Design**: Modern UI with TailwindCSS

## Tech Stack

### Backend
- **Django 5.2.4** - Web framework
- **Django REST Framework** - API development
- **PostgreSQL** - Primary database
- **SQLite** - Development database (fallback)
- **Pillow** - Image processing
- **django-cors-headers** - CORS handling

### Frontend
- **React 19** - UI framework
- **Vite** - Build tool and dev server
- **TailwindCSS** - Styling
- **React Router** - Navigation
- **Axios** - HTTP client
- **React Hook Form** - Form handling
- **Recharts** - Data visualization

### DevOps
- **Docker & Docker Compose** - Containerization
- **PostgreSQL** - Production database
- **Nginx** - Production web server
- **Gunicorn** - WSGI server

## Quick Start with Docker

### Prerequisites
- Docker and Docker Compose installed
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dealer
   ```

2. **Start development environment**
   ```bash
   # Start all services (PostgreSQL + Django + React)
   docker-compose -f docker-compose.dev.yml up --build
   ```

3. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000
   - Admin Panel: http://localhost:8000/admin

4. **Create a superuser** (in a new terminal)
   ```bash
   docker-compose -f docker-compose.dev.yml exec backend python manage.py createsuperuser
   ```

### Production Setup

1. **Build and start production services**
   ```bash
   docker-compose up --build -d
   ```

2. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000

## Manual Setup (Without Docker)

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd carconnect_backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set environment variables**
   ```bash
   export DEBUG=True
   export SECRET_KEY=your-secret-key
   # For PostgreSQL:
   export USE_POSTGRES=True
   export DB_NAME=dealerlink_db
   export DB_USER=dealerlink_user
   export DB_PASSWORD=dealerlink_pass
   export DB_HOST=localhost
   ```

5. **Run migrations**
   ```bash
   python manage.py migrate
   ```

6. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

7. **Start development server**
   ```bash
   python manage.py runserver
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd dealerlink-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login
- `POST /api/auth/logout/` - User logout
- `GET /api/auth/user/` - Get current user

### Cars
- `GET /api/cars/` - List cars
- `POST /api/cars/` - Create car listing
- `GET /api/cars/{id}/` - Get car details
- `PUT /api/cars/{id}/` - Update car listing
- `DELETE /api/cars/{id}/` - Delete car listing

### Saved Cars
- `GET /api/cars/saved/` - List saved cars
- `POST /api/cars/{id}/save/` - Save/unsave car

## Environment Variables

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Key variables:
- `DEBUG` - Enable/disable debug mode
- `SECRET_KEY` - Django secret key
- `USE_POSTGRES` - Use PostgreSQL instead of SQLite
- `DB_*` - Database connection settings
- `VITE_API_URL` - Frontend API URL

## Database

The application supports both SQLite (development) and PostgreSQL (production):

- **SQLite**: Default for development, no setup required
- **PostgreSQL**: Set `USE_POSTGRES=True` and configure DB_* variables

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
