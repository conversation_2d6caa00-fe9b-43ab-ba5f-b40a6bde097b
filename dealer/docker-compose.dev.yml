version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: dealerlink_db
      POSTGRES_USER: dealerlink_user
      POSTGRES_PASSWORD: dealerlink_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dealerlink_user -d dealerlink_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Backend (Development)
  backend:
    build: 
      context: ./carconnect_backend
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    environment:
      - DEBUG=True
      - USE_POSTGRES=True
      - DB_NAME=dealerlink_db
      - DB_USER=dealerlink_user
      - DB_PASSWORD=dealerlink_pass
      - DB_HOST=db
      - DB_PORT=5432
      - SECRET_KEY=django-insecure-docker-dev-key-change-in-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend
    volumes:
      - ./carconnect_backend:/app
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
    command: >
      sh -c "python manage.py migrate &&
             python manage.py runserver 0.0.0.0:8000"

  # React Frontend (Development)
  frontend:
    build:
      context: ./dealerlink-frontend
      dockerfile: Dockerfile.dev
    restart: unless-stopped
    environment:
      - VITE_API_URL=http://localhost:8000
    volumes:
      - ./dealerlink-frontend:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    depends_on:
      - backend
    command: npm run dev

volumes:
  postgres_data:
  media_volume:
