#!/bin/bash

# DealerLink Docker Development Helper Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to start development environment
start_dev() {
    print_status "Starting DealerLink development environment..."
    check_docker
    docker compose -f docker-compose.dev.yml up --build
}

# Function to start development environment in background
start_dev_bg() {
    print_status "Starting DealerLink development environment in background..."
    check_docker
    docker compose -f docker-compose.dev.yml up --build -d
    print_status "Services started. Access:"
    print_status "  Frontend: http://localhost:5173"
    print_status "  Backend:  http://localhost:8000"
    print_status "  Admin:    http://localhost:8000/admin"
}

# Function to stop development environment
stop_dev() {
    print_status "Stopping DealerLink development environment..."
    docker compose -f docker-compose.dev.yml down
}

# Function to restart development environment
restart_dev() {
    print_status "Restarting DealerLink development environment..."
    stop_dev
    start_dev_bg
}

# Function to view logs
logs() {
    docker compose -f docker-compose.dev.yml logs -f
}

# Function to run Django commands
django_cmd() {
    if [ $# -eq 0 ]; then
        print_error "Please provide a Django command"
        exit 1
    fi
    docker compose -f docker-compose.dev.yml exec backend python manage.py "$@"
}

# Function to create superuser
create_superuser() {
    print_status "Creating Django superuser..."
    django_cmd createsuperuser
}

# Function to run migrations
migrate() {
    print_status "Running Django migrations..."
    django_cmd migrate
}

# Function to make migrations
makemigrations() {
    print_status "Creating Django migrations..."
    django_cmd makemigrations
}

# Function to collect static files
collectstatic() {
    print_status "Collecting static files..."
    django_cmd collectstatic --noinput
}

# Function to run shell
shell() {
    print_status "Opening Django shell..."
    django_cmd shell
}

# Function to clean up Docker resources
cleanup() {
    print_warning "This will remove all stopped containers, unused networks, and dangling images."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up Docker resources..."
        docker system prune -f
        print_status "Cleanup complete."
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show help
show_help() {
    echo "DealerLink Docker Development Helper"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start         Start development environment (foreground)"
    echo "  start-bg      Start development environment (background)"
    echo "  stop          Stop development environment"
    echo "  restart       Restart development environment"
    echo "  logs          View logs from all services"
    echo "  migrate       Run Django migrations"
    echo "  makemigrations Create Django migrations"
    echo "  createsuperuser Create Django superuser"
    echo "  collectstatic Collect static files"
    echo "  shell         Open Django shell"
    echo "  cleanup       Clean up Docker resources"
    echo "  help          Show this help message"
    echo ""
    echo "Django commands:"
    echo "  django [cmd]  Run any Django management command"
    echo ""
    echo "Examples:"
    echo "  $0 start-bg"
    echo "  $0 django loaddata fixtures/sample_data.json"
    echo "  $0 django dbshell"
}

# Main script logic
case "${1:-help}" in
    start)
        start_dev
        ;;
    start-bg)
        start_dev_bg
        ;;
    stop)
        stop_dev
        ;;
    restart)
        restart_dev
        ;;
    logs)
        logs
        ;;
    migrate)
        migrate
        ;;
    makemigrations)
        makemigrations
        ;;
    createsuperuser)
        create_superuser
        ;;
    collectstatic)
        collectstatic
        ;;
    shell)
        shell
        ;;
    django)
        shift
        django_cmd "$@"
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
