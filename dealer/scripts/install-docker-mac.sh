#!/bin/bash

# Docker Installation Script for macOS
# This script helps install Docker Desktop on macOS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS only."
    exit 1
fi

print_info "Docker Installation Helper for macOS"
echo ""

# Check if Docker is already installed
if command -v docker &> /dev/null; then
    print_status "Docker is already installed!"
    docker --version
    exit 0
fi

print_status "Docker not found. Let's install it!"
echo ""

# Check if Homebrew is installed
if command -v brew &> /dev/null; then
    print_status "Homebrew found. Installing Docker Desktop via Homebrew..."
    
    # Install Docker Desktop
    brew install --cask docker
    
    print_status "Docker Desktop installed successfully!"
    print_warning "Please start Docker Desktop from Applications folder or Spotlight search."
    print_warning "After Docker Desktop starts, you can run: docker --version"
    
else
    print_warning "Homebrew not found. You have two options:"
    echo ""
    print_info "Option 1: Install Homebrew first, then Docker"
    echo "  1. Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    echo "  2. Install Docker: brew install --cask docker"
    echo ""
    print_info "Option 2: Download Docker Desktop manually"
    echo "  1. Visit: https://www.docker.com/products/docker-desktop/"
    echo "  2. Download Docker Desktop for Mac"
    echo "  3. Install the .dmg file"
    echo ""
    
    read -p "Would you like to install Homebrew now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        
        print_status "Homebrew installed. Now installing Docker Desktop..."
        brew install --cask docker
        
        print_status "Docker Desktop installed successfully!"
        print_warning "Please start Docker Desktop from Applications folder."
    else
        print_info "Please install Docker Desktop manually from: https://www.docker.com/products/docker-desktop/"
    fi
fi

echo ""
print_info "After Docker Desktop is running, you can:"
print_info "  1. Test Docker: docker --version"
print_info "  2. Start DealerLink: ./scripts/docker-dev.sh start-bg"
print_info "  3. Access the app at: http://localhost:5173"
